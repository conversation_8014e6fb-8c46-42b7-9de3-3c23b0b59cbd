export type AgentMode = 'chat' | 'agent' | 'agent-auto';

export interface AgentModeConfig {
  slug: AgentMode;
  name: string;
  description: string;
  icon: string;
  requiresApproval: boolean;
  allowedTools: string[];
}

export interface ToolApprovalRequest {
  id: string;
  tool: string;
  params: Record<string, any>;
  message: string;
  timestamp: number;
}

export interface AgentState {
  mode: AgentMode;
  isProcessing: boolean;
  pendingApproval?: ToolApprovalRequest;
  autoApprovalEnabled: boolean;
  maxAutoRequests: number;
  currentAutoRequests: number;
}

// Agent mode configurations based on Roo-Cline patterns
export const AGENT_MODES: AgentModeConfig[] = [
  {
    slug: 'chat',
    name: '💬 Chat',
    description: 'Ask questions and get responses',
    icon: 'i-ph:chat-circle',
    requiresApproval: false,
    allowedTools: ['ask_question', 'provide_response'],
  },
  {
    slug: 'agent',
    name: 'Agent',
    description: 'Autonomous with approval for some tools',
    icon: 'i-ph:robot',
    requiresApproval: true,
    allowedTools: [
      'read_file',
      'write_file',
      'execute_command',
      'search_files',
      'create_directory',
      'delete_file',
      'browser_action',
    ],
  },
  {
    slug: 'agent-auto',
    name: '⚡ Agent Auto',
    description: 'Fully autonomous mode',
    icon: 'i-ph:lightning',
    requiresApproval: false,
    allowedTools: [
      'read_file',
      'write_file',
      'execute_command',
      'search_files',
      'create_directory',
      'delete_file',
      'browser_action',
      'install_packages',
      'git_operations',
    ],
  },
];

// Tools that require approval in agent mode
export const APPROVAL_REQUIRED_TOOLS = [
  'write_file',
  'delete_file',
  'execute_command',
  'install_packages',
  'git_operations',
];

// Tools that are always safe and don't require approval
export const SAFE_TOOLS = [
  'read_file',
  'search_files',
  'list_files',
  'ask_question',
  'provide_response',
];

export function getAgentModeConfig(mode: AgentMode): AgentModeConfig {
  return AGENT_MODES.find(m => m.slug === mode) || AGENT_MODES[0];
}

export function isToolAllowed(tool: string, mode: AgentMode): boolean {
  const config = getAgentModeConfig(mode);
  return config.allowedTools.includes(tool);
}

export function requiresApproval(tool: string, mode: AgentMode): boolean {
  if (mode === 'chat') return false;
  if (mode === 'agent-auto') return false;
  if (mode === 'agent') {
    return APPROVAL_REQUIRED_TOOLS.includes(tool);
  }
  return false;
}
