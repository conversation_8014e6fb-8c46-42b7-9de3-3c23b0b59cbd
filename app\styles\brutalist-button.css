/* From Uiverse.io by 0xnihilism */
.brutalist-button {
  display: flex;
  align-items: center;
  width: 180px;
  height: 48px;
  background-color: #e3d19c;
  color: #89530e;
  text-decoration: none;
  font-family: Arial, sans-serif;
  font-weight: bold;
  border: 2px solid #ffffff;
  outline: 2px solid #1a1a1a;
  box-shadow: 5px 5px 0 #ff6c37;
  transition: all 0.2s ease-out;
  padding: 0 8px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.brutalist-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg at 50% 50%, #ff6c37, #9a77ff, #ff6c37);
  opacity: 0;
  transition: opacity 0.3s ease-out;
  z-index: 1;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.brutalist-button:hover::before {
  opacity: 0.15;
}

.brutalist-button:hover {
  transform: translate(-3px, -3px);
  box-shadow: 8px 8px 0 #ff6c37;
}

.brutalist-button:active {
  transform: translate(2px, 2px);
  box-shadow: 0px 0px 0 #ff6c37;
  background-color: #ffffff;
  color: #1a1a1a;
  border-color: #1a1a1a;
}

.claude-logo {
  display: flex;
  align-items: center;
  margin-right: 8px;
  position: relative;
  z-index: 2;
}

.starburst {
  font-size: 20px;
  color: #eb6c22;
  margin-right: 4px;
  text-shadow: 1px 1px 0 #1a1a1a;
  transition: transform 0.2s ease-out;
}

.brutalist-button:hover .starburst {
  transform: rotate(-15deg) scale(1.1);
}

.brutalist-button:active .starburst {
  transform: rotate(15deg) scale(0.9);
}

.claude-text {
  font-family: Georgia, serif;
  font-size: 16px;
  font-weight: normal;
  letter-spacing: 0.3px;
  text-shadow: 1px 1px 0 #e3d19c;
}

.button-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
  position: relative;
  z-index: 2;
}

.brutalist-button:hover .button-text {
  transform: skew(-5deg);
}

.brutalist-button:active .button-text {
  transform: skew(5deg);
}

.button-text span:first-child {
  font-size: 8px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.button-text span:last-child {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}
